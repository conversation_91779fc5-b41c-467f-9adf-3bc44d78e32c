# Тестова Система - Студентска Система за Управление

## 📝 **Студентска Тестова Функционалност**

### **Преглед на Тестове**
- **Статуси на тестове** - Четири основни състояния с цветово кодиране
  - **Планиран** - Тестът още не е започнал (сив цвят)
  - **Активен** - Тестът е достъпен за вземане (зелен цвят)
  - **Завършен** - Студентът е предал теста (син цвят)
  - **Изтекъл** - Времето за теста е изтекло (червен цвят)

- **Места за преглед** - Тестовете се показват на множество места
  - Страница "Моите задачи" с филтриране по статус
  - Курсови страници с интегрирани тестови секции
  - Известия с prominent показване на дати и часове
  - Прогрес страница с интеграция в финални оценки

### **Достъп до Тестове**
- **Време-базиран контрол** - Автоматично управление на достъпа
  - Countdown таймери до започване на теста
  - Проверка на активен период преди стартиране
  - Блокиране на достъп извън определените часове
  - Автоматично изтичане при край на периода

- **Сесийно управление** - Контрол на тестовите сесии
  - Уникален session ID за всяка тестова сесия
  - Проверка за вече завършени тестове
  - Възможност за продължаване на прекъснати сесии
  - Защита срещу множествено вземане на същия тест

### **Вземане на Тестове**
- **Интерфейс за тестване** - Интуитивен и функционален дизайн
  - Навигация между въпроси с Previous/Next бутони
  - Индикатор за прогрес и номер на текущия въпрос
  - Автоматично запазване на отговори при промяна
  - Предупреждения за неотговорени въпроси

- **Типове въпроси** - Поддръжка на различни формати
  - **Multiple Choice** - Избор от предварително зададени опции
  - **Text Answer** - Свободен текстов отговор
  - **True/False** - Двоичен избор Вярно/Невярно
  - Точкуване по въпрос с различни тегла

- **Таймер функционалност** - Прецизно управление на времето
  - Визуален countdown таймер в реално време
  - Предупреждения при приближаване на края
  - Автоматично подаване при изтичане на времето
  - Проверка на сървърно време за синхронизация

### **Подаване и Резултати**
- **Процес на подаване** - Сигурно и надеждно предаване
  - Ръчно подаване с потвърждение от студента
  - Автоматично подаване при изтичане на времето
  - Проверка за неотговорени въпроси преди подаване
  - Защита срещу случайно затваряне на страницата

- **Показване на резултати** - Незабавна обратна връзка
  - Общ резултат в точки и максимален брой точки
  - Процентно представяне на постижението
  - Време прекарано в теста
  - Индикация дали е подаден автоматично или ръчно

---

## 🎓 **Учителска Тестова Функционалност**

### **Създаване на Тестове**
- **Основна информация** - Задаване на тестови параметри
  - Заглавие и описание на теста
  - Избор на курс от dropdown меню
  - Продължителност в минути
  - Дати и часове за активност (start/end date)

- **Управление на въпроси** - Динамично добавяне и редактиране
  - Добавяне на неограничен брой въпроси
  - Избор на тип въпрос за всеки въпрос поотделно
  - Задаване на точки за всеки въпрос
  - Възможност за изтриване на въпроси
  - Задаване на правилни отговори за автоматично оценяване

- **Валидация и публикуване** - Проверки преди активиране
  - Валидация на времеви рамки
  - Проверка за минимум един въпрос
  - Потвърждение преди публикуване
  - Автоматично известяване на студентите

### **Планиране и Управление**
- **Моите планирани тестове** - Централизирано управление
  - Преглед на всички създадени тестове
  - Филтриране по курс и статус
  - Grid и List изгледи за различни предпочитания
  - Търсене по заглавие на тест

- **Статуси и контрол** - Мониторинг на тестовете
  - Същите статуси като при студентите (планиран/активен/изтекъл)
  - Възможност за редактиране на бъдещи тестове
  - Деактивиране на активни тестове при нужда
  - Статистики за участие в реално време

### **Резултати и Оценяване**
- **Обобщени резултати** - Бърз преглед на постиженията
  - Общ брой участници и процент участие
  - Средна оценка и разпределение на резултатите
  - Най-висок и най-нисък резултат
  - Статистики за автоматично vs ръчно подадени тестове

- **Детайлни резултати** - Пълна информация за всеки студент
  - Таблица с всички студентски резултати
  - Сортиране по различни критерии (име, резултат, време)
  - Филтриране по статус на подаване
  - Търсене по име или email на студент
  - Export функционалност за външни анализи

- **Индивидуални резултати** - Преглед на отговори по студент
  - Детайлен преглед на отговорите на всеки въпрос
  - Сравнение с правилните отговори
  - Време прекарано на всеки въпрос
  - Възможност за ръчно преоценяване при нужда

---

## 🔗 **Споделена Тестова Функционалност**

### **Време-базиран Достъп**
- **Синхронизация** - Еднакво време за всички потребители
  - Сървърно време като еталон за всички проверки
  - Българска часова зона за локални дати
  - Автоматично обновяване на статуси
  - Защита срещу манипулация на клиентско време

- **Гъвкаво планиране** - Различни сценарии за тестване
  - Тестове с фиксирани дати и часове
  - Различна продължителност за различни тестове
  - Възможност за припокриване на тестови периоди
  - Автоматично управление на изтекли тестове

### **Българско Форматиране**
- **Дати и часове** - Стандартизирано представяне
  - DD.MM.YYYY формат за дати
  - 24-часов формат за време (HH:MM)
  - Пълен формат DD.MM.YYYY HH:MM за datetime
  - Консистентност във всички интерфейси

- **Локализация** - Български език и култура
  - Всички съобщения и етикети на български
  - Българска терминология за академични понятия
  - Подходящи съобщения за грешки и успехи
  - Културно подходящи формулировки

### **Известия за Тестове**
- **Real-time обновления** - Актуална информация
  - Автоматично обновяване на всеки 30 секунди
  - Известия за нови тестове при публикуване
  - Спешни известия при приближаване на срокове
  - Потвърждения за успешно подадени тестове

- **Роле-специфични известия** - Различна информация за различни роли
  - **Студенти** - Достъпност, срокове, резултати
  - **Учители** - Подавания, завършвания, статистики
  - Интеграция с notification bell в интерфейса
  - Clickable известия с пренасочване към релевантни страници

### **Интеграция с Курсовете**
- **Курсово-базирано организиране** - Логическа структура
  - Тестовете са винаги свързани с конкретен курс
  - Показване в курсовите страници заедно с лекции и упражнения
  - Филтриране по курс във всички тестови интерфейси
  - Автоматично наследяване на курсови права и достъп

- **Оценъчна интеграция** - Част от общата оценка
  - Тестовете се включват в финалната оценка на курса
  - Българска 2-6 скала за преобразуване на резултати
  - Автоматично изчисляване на средни оценки
  - Интеграция с прогрес страницата за общ преглед

---

## 📊 **Ключови Възможности и Workflow-и**

### **Студентски Workflow**
1. **Откриване** - Получаване на известие за нов тест
2. **Планиране** - Преглед на дати и подготовка
3. **Достъп** - Влизане в теста в активния период
4. **Изпълнение** - Отговаряне на въпроси с таймер
5. **Подаване** - Ръчно или автоматично предаване
6. **Резултати** - Незабавен преглед на постижението

### **Учителски Workflow**
1. **Създаване** - Дефиниране на тест с въпроси и настройки
2. **Планиране** - Задаване на времеви рамки за активност
3. **Публикуване** - Активиране и известяване на студентите
4. **Мониторинг** - Следене на участието в реално време
5. **Анализ** - Преглед на резултати и статистики
6. **Оценяване** - Интеграция в общата курсова оценка

### **Системни Гаранции**
- **Сигурност** - Защита срещу измами и манипулации
- **Надеждност** - Автоматично запазване и backup на данни
- **Производителност** - Оптимизирано за множество едновременни потребители
- **Достъпност** - Работи на всички съвременни браузъри и устройства

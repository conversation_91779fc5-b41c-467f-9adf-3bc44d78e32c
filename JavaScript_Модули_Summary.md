# JavaScript Модули - Студентска Система за Управление

## 📚 **Студентски JavaScript Модули**

### **Автентификация и Сигурност**
- **auth.js** - Проверка на автентификация и пренасочване при неоторизиран достъп
- **profile-menu.js** - Управление на падащото меню на потребителския профил

### **Навигация и Интерфейс**
- **sidebar-courses.js** - Зареждане и показване на курсове в страничното меню
- **enroll-hover.js** - Hover панел за записване в курс с код
- **profile-menu.js** - Dropdown меню за потребителски профил

### **Управление на Курсове**
- **courses-core.js** - Основна логика за показване и пагинация на всички курсове
- **locked-course-core.js** - Функционалност за заключени курсове и заявка за код
- **unlocked-course-core.js** - Зареждане на съдържание за отключени курсове
- **my-courses.js** - Показване на записани курсове на студента

### **Тестове и Задачи**
- **test-taking.js** - Логика за вземане на тестове с таймер и автоматично подаване
- **test-utils.js** - Помощни функции за форматиране на дати и статус на тестове
- **my-tasks.js** - Показване на задачи, тестове и срокове с филтриране

### **Прогрес и Известия**
- **my-progress.js** - Визуализация на прогреса и статистики на студента
- **notifications.js** - Система за известия с real-time обновления на всеки 30 секунди

### **Профил и Настройки**
- **profile.js** - Управление на потребителски профил и качване на аватар
- **menu.js** - Основно меню и потребителска информация

### **Инициализация на Страници**
- **dashboard-init.js** - Инициализация на главното табло
- **courses-init.js** - Инициализация на страницата с всички курсове
- **locked-course-init.js** - Инициализация на заключени курсове
- **unlocked-course-init.js** - Инициализация на отключени курсове

---

## 🎓 **Учителски JavaScript Модули**

### **Автентификация и Сигурност**
- **auth.js** - Идентична функционалност като студентската версия
- **profile-menu.js** - Същата логика за dropdown меню

### **Навигация и Интерфейс**
- **sidebar-courses.js** - Зареждане на курсове с пренасочване към учителски шаблони
- **enroll-hover.js** - Същата функционалност за записване с код

### **Управление на Курсове**
- **courses-core.js** - Показване на курсове с достъп до всички за управление
- **unlocked-course-core.js** - Зареждане на курсове с учителски права
- **teacher-content-management.js** - CRUD операции за лекции, упражнения и курсови работи

### **Тестове и Оценяване**
- **create-test.js** - Създаване и планиране на тестове
- **my-scheduled-tests.js** - Управление на планирани тестове и резултати
- **test-taking.js** - Преглед на тестове (без функция за вземане)
- **test-utils.js** - Помощни функции за дати и статуси (учителска версия)

### **Табло и Статистики**
- **teacher-dashboard.js** - Учителско табло с статистики и управление
- **notifications.js** - Система за известия за учители

### **Профил и Настройки**
- **profile.js** - Управление на профил с EmailJS за изпращане на кодове
- **menu.js** - Потребителско меню с учителски права

### **Инициализация на Страници**
- **dashboard-init.js** - Инициализация на учителското табло
- **courses-init.js** - Инициализация на страницата с курсове
- **teacher-unlocked-course-init.js** - Инициализация на учителски курсове
- **unlocked-course-init.js** - Основна инициализация за курсове

---

## 🔗 **Споделени JavaScript Модули**

### **Автентификация и Регистрация**
- **login.js** - Логика за вход в системата
- **registration-handler.js** - Обработка на регистрация на нови потребители

### **Backend Логика**
- **server.js** - Node.js/Express сървър с JWT автентификация и Cloudinary интеграция

---

## 📋 **Ключови Функционалности по Модули**

### **Автентификация**
- Cookie-базирана JWT проверка
- Автоматично пренасочване при изтекла сесия
- Logout функционалност

### **Управление на Курсове**
- Пагинация и филтриране
- Заключени/отключени курсове
- Записване с код

### **Тестова Система**
- Планиране и достъп базиран на време
- Автоматично подаване при изтичане
- Countdown таймери

### **Известия**
- Real-time обновления
- Роле-специфични известия
- Спешни известия за тестове

### **Файлово Управление**
- Cloudinary интеграция
- Качване на аватари и съдържание
- Автоматично компресиране

### **Дати и Време**
- Българско форматиране (DD.MM.YYYY HH:MM)
- 24-часов формат
- Стандартизирани помощни функции
